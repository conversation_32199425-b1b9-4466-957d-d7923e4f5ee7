<template>
  <div class="app-container">
    <!-- 左侧菜单栏 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <div class="logo-icon">
            <div class="i-mdi-waves text-2xl"></div>
          </div>
          <div class="logo-text">
            <div class="logo-title">银海集团</div>
            <div class="logo-subtitle">数据大屏</div>
          </div>
        </div>
      </div>

      <div class="sidebar-content">
        <nav class="menu-nav">
          <div
            v-for="item in menuItems"
            :key="item.key"
            class="menu-item"
            :class="{ active: currentPage === item.key }"
            @click="switchPage(item.key)"
          >
            <div class="menu-icon">
              <div :class="item.icon"></div>
            </div>
            <div class="menu-text">
              <div class="menu-title">{{ item.title }}</div>
              <div class="menu-subtitle">{{ item.subtitle }}</div>
            </div>
            <div class="menu-indicator"></div>
          </div>
        </nav>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="main-content">
      <AquacultureHomepage
        v-if="currentPage === 'homepage'"
        @switch-to-dashboard2="switchToDashboard2"
      />
      <Dashboard2
        v-else-if="currentPage === 'dashboard2'"
        @switch-to-homepage="switchToHomepage"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import AquacultureHomepage from "./pages/aquaculture-homepage.vue";
import Dashboard2 from "./pages/aquaculture-dashboard2.vue";
import autofit from "autofit.js";

// 菜单项类型定义
interface MenuItem {
  key: string;
  title: string;
  subtitle: string;
  icon: string;
}

// 菜单项数据
const menuItems = ref<MenuItem[]>([
  {
    key: "homepage",
    title: "总览",
    subtitle: "数据概览",
    icon: "i-mdi-view-dashboard text-xl",
  },
]);

// 当前页面状态
const currentPage = ref<string>("homepage");

// 页面切换函数
const switchPage = (pageKey: string) => {
  currentPage.value = pageKey;
};

// 切换到 dashboard2 (保持向后兼容)
const switchToDashboard2 = () => {
  currentPage.value = "dashboard2";
};

// 切换到主页 (保持向后兼容)
const switchToHomepage = () => {
  currentPage.value = "homepage";
};

autofit.init();
</script>

<style scoped>
/* 应用容器 */
.app-container {
  display: flex;
  overflow: hidden;
  background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
  width: 100%;
  height: 100%;
}

/* 侧边栏 */
.sidebar {
  width: 280px;
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(102, 204, 255, 0.15) 0%,
    rgba(102, 204, 255, 0.08) 50%,
    rgba(102, 204, 255, 0.05) 100%
  );
  border-right: 2px solid rgba(102, 204, 255, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.sidebar::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(
    180deg,
    transparent 0%,
    #66ccff 20%,
    #66ccff 80%,
    transparent 100%
  );
  box-shadow: 0 0 20px #66ccff;
}

/* 侧边栏头部 */
.sidebar-header {
  padding-left: 20px;
  height: 80px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(102, 204, 255, 0.2);
  position: relative;
}

.sidebar-header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 20px;
  right: 20px;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #66ccff 50%,
    transparent 100%
  );
  box-shadow: 0 0 10px rgba(102, 204, 255, 0.5);
}

/* Logo区域 */
.logo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #66ccff, #33aaff);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #001122;
  box-shadow: 0 0 20px rgba(102, 204, 255, 0.6),
    inset 0 0 20px rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.logo-icon::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 70%
  );
  animation: logoShine 3s ease-in-out infinite;
}

@keyframes logoShine {
  0%,
  100% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  50% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

.logo-text {
  flex: 1;
}

.logo-title {
  font-size: 20px;
  font-weight: bold;
  color: #66ccff;
  text-shadow: 0 0 15px rgba(102, 204, 255, 0.8);
  margin-bottom: 4px;
  letter-spacing: 1px;
}

.logo-subtitle {
  font-size: 12px;
  color: rgba(102, 204, 255, 0.7);
  letter-spacing: 2px;
}

/* 侧边栏内容 */
.sidebar-content {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
  overflow-x: hidden;
}

.sidebar-content::-webkit-scrollbar {
  width: 4px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: rgba(102, 204, 255, 0.1);
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #66ccff, #33aaff);
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(102, 204, 255, 0.5);
}

/* 菜单导航 */
.menu-nav {
  padding: 0 16px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  margin-bottom: 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  background: rgba(102, 204, 255, 0.05);
  border: 1px solid rgba(102, 204, 255, 0.1);
}

.menu-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(102, 204, 255, 0.1) 50%,
    transparent 100%
  );
  transition: left 0.5s ease;
}

.menu-item:hover::before {
  left: 100%;
}

.menu-item:hover {
  background: rgba(102, 204, 255, 0.12);
  border-color: rgba(102, 204, 255, 0.3);
  box-shadow: 0 8px 25px rgba(102, 204, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.menu-item.active {
  background: linear-gradient(
    135deg,
    rgba(102, 204, 255, 0.2) 0%,
    rgba(102, 204, 255, 0.1) 100%
  );
  border-color: #66ccff;
  box-shadow: 0 12px 30px rgba(102, 204, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.menu-item.active .menu-indicator {
  opacity: 1;
  transform: scaleY(1);
}

.menu-item.active .menu-title {
  color: #66ccff;
  text-shadow: 0 0 10px rgba(102, 204, 255, 0.6);
}

.menu-item.active .menu-icon {
  color: #66ccff;
  text-shadow: 0 0 15px rgba(102, 204, 255, 0.8);
  transform: scale(1.1);
}

/* 菜单图标 */
.menu-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(102, 204, 255, 0.8);
  transition: all 0.3s ease;
  margin-right: 16px;
}

/* 菜单文本 */
.menu-text {
  flex: 1;
}

.menu-title {
  font-size: 16px;
  font-weight: 600;
  color: rgba(102, 204, 255, 0.9);
  margin-bottom: 2px;
  transition: all 0.3s ease;
  letter-spacing: 0.5px;
}

.menu-subtitle {
  font-size: 12px;
  color: rgba(102, 204, 255, 0.6);
  letter-spacing: 1px;
}

/* 菜单指示器 */
.menu-indicator {
  width: 4px;
  height: 24px;
  background: linear-gradient(180deg, #66ccff, #33aaff);
  border-radius: 2px;
  opacity: 0;
  transform: scaleY(0);
  transition: all 0.3s ease;
  box-shadow: 0 0 10px rgba(102, 204, 255, 0.6);
}

/* 主内容区域 */
.main-content {
  flex: 1;
  height: 100%;
  overflow: hidden;
  position: relative;
}
</style>
